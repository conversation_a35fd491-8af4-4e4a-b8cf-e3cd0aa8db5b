{"name": "@vben/web-ele", "version": "5.5.4", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-ele"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "dev:demo1": "cd src/modules/demo1 && pnpm dev", "build:demo1": "cd src/modules/demo1 && pnpm build", "typecheck:demo1": "cd src/modules/demo1 && pnpm typecheck"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@highlightjs/vue-plugin": "^2.1.2", "@tinymce/tinymce-vue": "^6.0.1", "@vben-core/shadcn-ui": "workspace:*", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "dayjs": "catalog:", "element-plus": "catalog:", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "pinia": "catalog:", "tinymce": "^7.3.0", "vform3-builds": "^3.0.10", "vue": "catalog:", "vue-router": "catalog:", "cropperjs": "^1.6.2"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "unplugin-element-plus": "catalog:"}}