# Demo1 模块集成指南

## 配置完成 ✅

Demo1 模块现在已经正确配置，可以：

1. **拥有独立的依赖包环境** - 有自己的 package.json 和依赖管理
2. **嵌入到 web-ele 路由中** - 已在 `local.ts` 中配置路由
3. **引入主应用 API** - 可以使用 `#/` 路径导入主应用资源
4. **拥有自己的 API** - 模块内部的 API 服务

## 核心配置

### package.json
```json
{
  "name": "@app/demo1",
  "dependencies": {
    "@vben/request": "workspace:*",
    "@vben/types": "workspace:*", 
    "@vben/utils": "workspace:*",
    "@vueuse/core": "catalog:",
    "dayjs": "catalog:",
    "echarts": "catalog:",
    "lodash-es": "catalog:"
  }
}
```

### tsconfig.json
```json
{
  "extends": "../../../tsconfig.json",
  "compilerOptions": {
    "baseUrl": "../../..//",
    "paths": {
      "#/*": ["./src/*"],  // 指向 web-ele/src
      "@/*": ["./src/modules/demo1/src/*"],  // 指向模块内部
      "@modules/*": ["./src/modules/*"],
      "@demo1/*": ["./src/modules/demo1/src/*"]
    }
  }
}
```

## 使用方式

### 1. 引入主应用 API
```typescript
// 在 demo1 模块中引入主应用的 requestClient
import { requestClient } from '#/api/request';

// 使用主应用的其他资源
import { someUtil } from '#/utils/common';
import { useUserStore } from '#/store/user';
```

### 2. 模块自己的 API
```typescript
// demo1 模块的 API 服务
export const getUserInfo = async (userId: number) => {
  return requestClient.get(`/demo1/user/${userId}`);
};

export const getStatistics = async () => {
  return requestClient.get('/demo1/statistics');
};
```

### 3. 在主应用中使用模块
```typescript
// 导入模块组件
import { Demo1Overview, Demo1Element, Demo1Form } from '@demo1';

// 导入模块配置和状态
import { useDemo1Store, getModuleConfig } from '@demo1';
```

## 路由配置

模块已嵌入到主应用路由中：

```typescript
// apps/web-ele/src/router/routes/local.ts
{
  component: 'BasicLayout',
  name: 'Demo1',
  path: '/demo1',
  children: [
    {
      name: 'Demo1Overview',
      path: '/demo1/overview',
      component: '/modules/demo1/views/index',
    },
    // ...其他路由
  ],
}
```

## 关键解决方案

### 问题：`#/*` 路径映射冲突
**解决**：Demo1 模块继承主应用的 tsconfig.json，使用相同的 `#/*` 映射指向 `web-ele/src`

### 问题：API 导入路径错误  
**解决**：使用 `#/api/request` 而不是相对路径，利用主应用的路径映射

### 问题：独立依赖管理
**解决**：Demo1 有自己的 package.json，依赖会安装到模块目录或被 monorepo 提升

## 开发流程

1. **开发模块功能** - 在 `src/modules/demo1` 中开发
2. **使用主应用资源** - 通过 `#/` 路径导入
3. **类型检查** - `pnpm typecheck` 验证类型正确性
4. **集成测试** - 在主应用中测试模块功能

现在 Demo1 模块完全符合您的需求：有独立依赖、嵌入路由、可引入主应用 API！🎉
