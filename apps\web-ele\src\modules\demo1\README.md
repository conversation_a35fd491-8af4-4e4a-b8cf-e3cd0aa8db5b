# Demo1 子模块

Demo1 是 web-ele 应用的一个独立子模块，支持独立开发、配置和部署。

## 功能特性

- ✅ **独立开发环境** - 支持独立启动和调试
- ✅ **自定义配置** - 独立的环境变量和配置管理
- ✅ **热重载支持** - 开发时自动重载
- ✅ **类型安全** - 完整的 TypeScript 支持
- ✅ **模块化架构** - 清晰的模块边界和接口

## 目录结构

```
demo1/
├── src/
│   ├── api/           # API 接口
│   ├── components/    # 组件
│   ├── config/        # 配置文件
│   ├── store/         # 状态管理
│   ├── types/         # 类型定义
│   ├── utils/         # 工具函数
│   ├── views/         # 页面组件
│   ├── bootstrap.ts   # 模块启动文件
│   ├── main.ts        # 独立运行入口
│   └── index.ts       # 模块导出
├── .env               # 基础环境变量
├── .env.development   # 开发环境变量
├── .env.production    # 生产环境变量
├── index.html         # 独立运行模板
├── package.json       # 模块依赖配置
├── tsconfig.json      # TypeScript 配置
├── vite.config.mts    # Vite 配置
└── README.md          # 说明文档
```

## 快速开始

### 1. 安装依赖

在项目根目录执行：

```bash
pnpm install
```

### 2. 独立开发

启动 Demo1 模块的独立开发服务器：

```bash
# 在主应用中启动
pnpm dev:demo1

# 或者进入模块目录启动
cd src/modules/demo1
pnpm dev
```

访问 http://localhost:5174 查看模块。

### 3. 在主应用中使用

```typescript
// 导入模块组件
import { Demo1Overview, Demo1Element, Demo1Form } from '@demo1';

// 导入模块配置
import { getModuleConfig } from '@demo1';

// 导入模块路由
import { moduleRoutes } from '@demo1';
```

## 配置说明

### 环境变量

模块支持以下环境变量：

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `VITE_APP_TITLE` | 应用标题 | Demo1 模块 |
| `VITE_API_BASE_URL` | API 基础地址 | /api/demo1 |
| `VITE_DEMO1_THEME_COLOR` | 主题色 | #409EFF |
| `VITE_ENABLE_DEBUG` | 调试模式 | true |

### 运行时配置

```typescript
import { getModuleConfig, updateRuntimeConfig } from '@demo1';

// 获取配置
const config = getModuleConfig();

// 更新运行时配置
updateRuntimeConfig({
  pageSize: 50,
  autoSave: false,
});
```

## 开发指南

### 添加新页面

1. 在 `src/views/` 下创建页面组件
2. 在 `src/index.ts` 中添加路由配置
3. 导出组件供外部使用

### 添加 API 接口

1. 在 `src/api/` 下创建接口文件
2. 使用模块配置的 API 基础地址
3. 在 `src/index.ts` 中导出接口

### 自定义配置

1. 修改 `.env*` 文件中的环境变量
2. 在 `src/config/index.ts` 中添加配置项
3. 使用 `getModuleConfig()` 获取配置

## 构建部署

### 构建模块

```bash
# 构建为库文件
pnpm build:demo1

# 或者进入模块目录构建
cd src/modules/demo1
pnpm build
```

### 类型检查

```bash
# 检查模块类型
pnpm typecheck:demo1

# 或者进入模块目录检查
cd src/modules/demo1
pnpm typecheck
```

## 最佳实践

1. **模块边界** - 保持模块的独立性，避免与主应用强耦合
2. **配置管理** - 使用环境变量管理不同环境的配置
3. **类型安全** - 为所有接口和配置提供类型定义
4. **错误处理** - 实现完善的错误处理机制
5. **性能优化** - 合理使用懒加载和代码分割

## 故障排除

### 常见问题

1. **端口冲突** - 修改 `vite.config.mts` 中的端口配置
2. **路径解析错误** - 检查 `tsconfig.json` 中的路径映射
3. **环境变量不生效** - 确保变量名以 `VITE_` 开头

### 调试技巧

1. 启用调试模式：设置 `VITE_ENABLE_DEBUG=true`
2. 查看模块配置：在控制台执行 `console.log(getModuleConfig())`
3. 检查路由配置：确保路由路径和组件路径正确

## 更新日志

### v0.0.1 (2024-01-XX)

- ✨ 初始版本
- ✨ 支持独立开发环境
- ✨ 完整的配置管理
- ✨ TypeScript 支持
