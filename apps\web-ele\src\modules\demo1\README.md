# Demo1 子模块

Demo1 是 web-ele 应用的功能子模块，拥有独立的依赖环境和完整的模块架构。

## 功能特性

- ✅ **独立依赖环境** - 拥有自己的 package.json 和 node_modules
- ✅ **模块化配置** - 独立的配置管理系统
- ✅ **状态管理** - 基于 Pinia 的模块状态管理
- ✅ **API 服务** - 完整的 API 接口封装
- ✅ **类型安全** - 完整的 TypeScript 支持
- ✅ **组件导出** - 可复用的 Vue 组件

## 目录结构

```text
demo1/
├── src/
│   ├── api/           # API 接口服务
│   ├── config/        # 模块配置
│   ├── store/         # Pinia 状态管理
│   ├── types/         # TypeScript 类型定义
│   ├── utils/         # 工具函数
│   ├── views/         # Vue 页面组件
│   └── index.ts       # 模块主导出文件
├── node_modules/      # 模块独立依赖
├── package.json       # 模块依赖配置
├── tsconfig.json      # TypeScript 配置
└── README.md          # 说明文档
```

## 快速开始

### 1. 安装依赖

在模块目录下安装依赖：

```bash
cd apps/web-ele/src/modules/demo1
pnpm install
```

### 2. 在主应用中集成

```typescript
// 导入模块组件
import { Demo1Overview, Demo1Element, Demo1Form } from '@demo1';

// 导入模块配置和状态管理
import { getModuleConfig, useDemo1Store } from '@demo1';

// 导入模块路由
import { moduleRoutes, initDemo1Module } from '@demo1';

// 初始化模块
const demo1Module = initDemo1Module();
```

### 3. 使用模块组件

```vue
<template>
  <div>
    <!-- 使用 Demo1 组件 -->
    <Demo1Overview />
    <Demo1Element />
    <Demo1Form />
  </div>
</template>

<script setup>
import { Demo1Overview, Demo1Element, Demo1Form } from '@demo1';
</script>
```

## 配置说明

### 模块配置

```typescript
import { getModuleConfig, updateRuntimeConfig } from '@demo1';

// 获取模块配置
const config = getModuleConfig();
console.log(config.name); // 'demo1'
console.log(config.api.baseURL); // '/api/demo1'

// 更新运行时配置
updateRuntimeConfig({
  pageSize: 50,
  autoSave: false,
  theme: 'dark',
});
```

### 状态管理

```typescript
import { useDemo1Store } from '@demo1';

// 在组件中使用
const demo1Store = useDemo1Store();

// 访问状态
console.log(demo1Store.state.userInfo);
console.log(demo1Store.formattedRevenue);

// 调用方法
demo1Store.updateUserInfo({ name: '新用户名' });
demo1Store.refreshStatistics();
```

## 开发指南

### 添加新组件

1. 在 `src/views/` 下创建 Vue 组件
2. 在 `src/index.ts` 中导出组件
3. 在 `moduleRoutes` 中添加路由配置

### 添加 API 接口

1. 在 `src/api/index.ts` 中添加新的 API 函数
2. 使用模块配置的 API 基础地址
3. 在 Store 中调用 API 接口

### 扩展状态管理

1. 在 `src/store/index.ts` 中添加新的状态
2. 实现相应的 getter 和 action
3. 在组件中使用 `useDemo1Store()` 访问

### 自定义配置

1. 在 `src/config/index.ts` 中修改 `moduleConfig`
2. 添加新的配置项和类型定义
3. 使用 `getModuleConfig()` 获取配置

## 类型检查

```bash
# 检查模块类型
pnpm typecheck:demo1

# 或者进入模块目录检查
cd src/modules/demo1
pnpm typecheck
```

## 最佳实践

1. **模块边界** - 保持模块的独立性，通过明确的接口与主应用交互
2. **依赖管理** - 合理使用 dependencies 和 peerDependencies
3. **类型安全** - 为所有接口和配置提供完整的类型定义
4. **状态管理** - 使用 Pinia store 管理模块状态，避免全局污染
5. **配置管理** - 集中管理模块配置，支持运行时动态修改

## 故障排除

### 常见问题

1. **类型错误** - 检查 `tsconfig.json` 中的路径映射配置
2. **依赖缺失** - 确保模块依赖已正确安装
3. **导入错误** - 检查模块导出和主应用中的导入路径

### 调试技巧

1. 查看模块配置：`console.log(getModuleConfig())`
2. 检查模块状态：使用 Vue DevTools 查看 Pinia store
3. 验证模块初始化：`console.log(initDemo1Module())`

## 更新日志

### v0.0.1 (2024-01-XX)

- ✨ 初始版本
- ✨ 独立的依赖环境
- ✨ 完整的模块架构
- ✨ Pinia 状态管理
- ✨ TypeScript 支持
