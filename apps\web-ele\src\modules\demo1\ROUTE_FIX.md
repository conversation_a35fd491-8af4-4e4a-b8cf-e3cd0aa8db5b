# Demo1 模块路由配置修复

## 问题分析

Demo1 模块的路由无法正确加载，经过分析发现是路径映射问题。

## 根本原因

### 1. 路由组件解析流程

1. **pageMap 生成**：`import.meta.glob('../modules/**/*.vue')` 扫描组件
   - 扫描到：`../modules/demo1/src/views/index.vue`
   - 经过 `normalizeViewPath` 处理：`/modules/demo1/src/views/index.vue`

2. **路由配置**：`component: '/modules/demo1/src/views/index'`
   - 经过 `normalizeViewPath` 处理：`/modules/demo1/src/views/index`
   - 添加 `.vue` 后缀：`/modules/demo1/src/views/index.vue`

3. **匹配过程**：在 `pageMap` 中查找 `/modules/demo1/src/views/index.vue`

### 2. normalizeViewPath 函数逻辑

```typescript
function normalizeViewPath(path: string): string {
  // 去除相对路径前缀
  const normalizedPath = path.replace(/^(\.\/|\.\.\/)+/, '');
  
  // 确保路径以 '/' 开头
  const viewPath = normalizedPath.startsWith('/')
    ? normalizedPath
    : `/${normalizedPath}`;
  
  // 移除 /views 前缀（这里是关键）
  return viewPath.replace(/^\/views/, '');
}
```

## 解决方案

### 修复前的错误配置

```typescript
// ❌ 错误：缺少 src 路径
{
  name: 'Demo1Overview',
  path: '/demo1/overview',
  component: '/modules/demo1/views/index',  // 缺少 src
}
```

### 修复后的正确配置

```typescript
// ✅ 正确：包含完整路径
{
  name: 'Demo1Overview',
  path: '/demo1/overview',
  component: '/modules/demo1/src/views/index',  // 包含 src
}
```

## 路径映射对照表

| 组件文件实际位置 | pageMap 扫描结果 | normalizeViewPath 处理后 | 路由配置 | 最终查找键 |
|-----------------|------------------|-------------------------|----------|-----------|
| `src/modules/demo1/src/views/index.vue` | `../modules/demo1/src/views/index.vue` | `/modules/demo1/src/views/index.vue` | `/modules/demo1/src/views/index` | `/modules/demo1/src/views/index.vue` |

## 修复内容

### 1. 更新路由配置文件

文件：`apps/web-ele/src/router/routes/local.ts`

```typescript
// Demo1 模块路由配置
{
  component: 'BasicLayout',
  name: 'Demo1',
  path: '/demo1',
  children: [
    {
      name: 'Demo1Overview',
      path: '/demo1/overview',
      component: '/modules/demo1/src/views/index',  // ✅ 修复：添加 src
      meta: { title: '模块概览' },
    },
    {
      name: 'Demo1Element', 
      path: '/demo1/element',
      component: '/modules/demo1/src/views/element/index',  // ✅ 修复：添加 src
      meta: { title: 'Element Plus演示' },
    },
    {
      name: 'Demo1Form',
      path: '/demo1/form', 
      component: '/modules/demo1/src/views/form/basic',  // ✅ 修复：添加 src
      meta: { title: '表单演示' },
    },
  ],
}
```

### 2. 确认组件文件存在

- ✅ `apps/web-ele/src/modules/demo1/src/views/index.vue`
- ✅ `apps/web-ele/src/modules/demo1/src/views/element/index.vue`  
- ✅ `apps/web-ele/src/modules/demo1/src/views/form/basic.vue`

### 3. 确认 pageMap 配置

文件：`apps/web-ele/src/router/access.ts`

```typescript
const pageMap: ComponentRecordType = {
  ...import.meta.glob('../views/**/*.vue'),
  ...import.meta.glob('../modules/**/*.vue'),  // ✅ 正确扫描 modules 目录
};
```

## 验证方法

1. **启动应用**：`pnpm dev`
2. **访问路由**：
   - `/demo1/overview` - 模块概览
   - `/demo1/element` - Element Plus演示  
   - `/demo1/form` - 表单演示
3. **检查控制台**：确保没有 "route component is invalid" 错误

## 关键要点

1. **路径一致性**：路由配置中的组件路径必须与 pageMap 扫描到的路径一致
2. **src 目录**：Demo1 模块的组件在 `src/views` 下，路由配置必须包含 `src`
3. **normalizeViewPath**：理解这个函数的处理逻辑对路径配置至关重要
4. **调试技巧**：查看控制台错误信息 "route component is invalid" 来定位问题

现在 Demo1 模块的路由应该可以正常加载了！🎉
