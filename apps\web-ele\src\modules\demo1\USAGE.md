# Demo1 模块使用指南

## 配置化路径解决方案

Demo1 模块现在支持通过配置来指向 web-ele 目录，解决了 API 路径错误问题。

## 核心特性

### 1. 路径配置

```typescript
import { moduleConfig, resolveWebElePath } from '@demo1';

// 查看路径配置
console.log(moduleConfig.paths);
// {
//   webEleRoot: '../../../',
//   webEleApi: '../../../api',
//   webEleUtils: '../../../utils',
//   webEleStore: '../../../store'
// }

// 解析路径
const apiPath = resolveWebElePath('api/request');
console.log(apiPath); // '../../../api/request'
```

### 2. 动态导入主应用模块

```typescript
import { importFromWebEle } from '@demo1';

// 动态导入主应用的 API
const mainAppApi = await importFromWebEle('api/request');
console.log(mainAppApi.requestClient);

// 动态导入主应用的工具函数
const mainAppUtils = await importFromWebEle('utils/index');
console.log(mainAppUtils);
```

### 3. 智能 API 客户端

```typescript
import { createRequestClient, getRequestClient } from '@demo1';

// 创建请求客户端（自动选择主应用或备用客户端）
const client = await createRequestClient();

// 使用客户端
const response = await client.get('/demo1/users');

// 或者使用便捷方法
import { requestClient } from '@demo1';
const users = await requestClient.get('/demo1/users');
```

## 配置选项

### 启用/禁用主应用客户端

```typescript
import { moduleConfig } from '@demo1';

// 修改配置
moduleConfig.api.useMainAppClient = false; // 强制使用备用客户端
```

### 自定义路径

```typescript
import { moduleConfig } from '@demo1';

// 修改路径配置（如果需要）
moduleConfig.paths.webEleApi = '../../../custom-api';
```

## 在主应用中集成

### 1. 导入模块

```typescript
// 在主应用中导入 Demo1 模块
import { 
  Demo1Overview, 
  Demo1Element, 
  Demo1Form,
  initDemo1Module,
  useDemo1Store,
  moduleRoutes
} from '@demo1';

// 初始化模块
const demo1Module = initDemo1Module();
console.log(demo1Module);
```

### 2. 使用组件

```vue
<template>
  <div>
    <Demo1Overview />
    <Demo1Element />
    <Demo1Form />
  </div>
</template>

<script setup>
import { Demo1Overview, Demo1Element, Demo1Form } from '@demo1';
</script>
```

### 3. 使用状态管理

```typescript
import { useDemo1Store } from '@demo1';

// 在组件中使用
const demo1Store = useDemo1Store();

// 访问状态
console.log(demo1Store.state.userInfo);

// 调用方法
await demo1Store.refreshStatistics();
```

## 故障排除

### 1. API 导入失败

如果看到警告 "无法导入主应用的 requestClient，使用备用客户端"，这是正常的。模块会自动降级到备用客户端。

### 2. 路径解析错误

检查 `moduleConfig.paths` 配置是否正确指向 web-ele 目录。

### 3. 类型错误

确保 TypeScript 配置正确，特别是 `paths` 映射。

## 最佳实践

1. **优先使用主应用客户端**：保持 `useMainAppClient: true` 以获得最佳兼容性
2. **路径配置集中管理**：通过 `moduleConfig.paths` 统一管理路径
3. **错误处理**：使用 try-catch 处理动态导入失败的情况
4. **类型安全**：充分利用 TypeScript 类型检查

## 配置总结

✅ **路径配置** - 通过 `moduleConfig.paths` 指向 web-ele 目录  
✅ **动态导入** - 支持运行时导入主应用模块  
✅ **智能客户端** - 自动选择最佳 API 客户端  
✅ **类型安全** - 完整的 TypeScript 支持  
✅ **错误处理** - 优雅的降级机制  

现在 Demo1 模块可以正确访问 web-ele 的 API 和其他资源了！
