<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Demo1 模块 - 独立开发环境</title>
  <meta name="description" content="Demo1 子模块独立开发环境" />
  <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
  <style>
    /* 基础样式重置 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f5f5f5;
    }
    
    #app {
      min-height: 100vh;
      padding: 20px;
    }
    
    /* 加载动画 */
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      font-size: 18px;
      color: #409EFF;
    }
    
    .loading::after {
      content: '';
      width: 20px;
      height: 20px;
      margin-left: 10px;
      border: 2px solid #409EFF;
      border-top: 2px solid transparent;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="loading">Demo1 模块加载中...</div>
  </div>
  <script type="module" src="/src/main.ts"></script>
</body>
</html>
