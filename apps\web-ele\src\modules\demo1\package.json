{"name": "@app/demo1", "version": "0.0.1", "description": "Demo1 子模块 - web-ele 应用的功能模块", "private": true, "type": "module", "scripts": {"build": "tsc --noEmit", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "main": "./dist/index.js", "module": "./dist/index.js", "imports": {"#/*": "./src/*"}, "exports": {".": "./src/index.ts", "./*": "./*"}, "publishConfig": {"exports": {".": "./dist/index.js", "./*": "./*"}}, "peerDependencies": {"@vben/request": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "element-plus": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "dependencies": {"@vueuse/core": "catalog:", "dayjs": "catalog:", "echarts": "catalog:", "lodash-es": "catalog:"}, "devDependencies": {"@vben/tsconfig": "workspace:*", "typescript": "catalog:", "vue-tsc": "catalog:"}}