{"name": "@app/demo1", "version": "0.0.1", "description": "Demo1 子模块 - 支持独立开发和配置", "private": true, "type": "module", "scripts": {"dev": "vite --mode development --config vite.config.mts", "build": "vite build --mode production --config vite.config.mts", "preview": "vite preview --config vite.config.mts", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "main": "./dist/index.js", "module": "./dist/index.js", "imports": {"#/*": "./src/*"}, "exports": {".": "./src/index.ts", "./*": "./*"}, "publishConfig": {"exports": {".": "./dist/index.js", "./*": "./*"}}, "dependencies": {"@vben/request": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "dayjs": "catalog:", "element-plus": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@vben/tsconfig": "workspace:*", "@vben/vite-config": "workspace:*", "typescript": "catalog:", "vite": "catalog:", "vue-tsc": "catalog:"}}