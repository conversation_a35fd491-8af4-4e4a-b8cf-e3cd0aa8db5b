/**
 * Demo1 模块 API 客户端
 * 支持动态导入主应用的 requestClient
 */

import { importFromWebEle, moduleConfig } from '../config';

// 请求客户端接口
interface RequestClient {
  get(url: string, options?: any): Promise<any>;
  post(url: string, data?: any, options?: any): Promise<any>;
  put(url: string, data?: any, options?: any): Promise<any>;
  delete(url: string, options?: any): Promise<any>;
}

// 简化的请求客户端实现（备用）
const createFallbackClient = (): RequestClient => {
  const request = async (url: string, options: any = {}) => {
    const { method = 'GET', data, params } = options;

    // 构建查询字符串
    let queryString = '';
    if (params) {
      queryString = `?${new URLSearchParams(params).toString()}`;
    }

    const fetchOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    if (data && method !== 'GET') {
      fetchOptions.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(`${url}${queryString}`, fetchOptions);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  };

  return {
    get: (url: string, options?: any) =>
      request(url, { ...options, method: 'GET' }),
    post: (url: string, data?: any, options?: any) =>
      request(url, { ...options, method: 'POST', data }),
    put: (url: string, data?: any, options?: any) =>
      request(url, { ...options, method: 'PUT', data }),
    delete: (url: string, options?: any) =>
      request(url, { ...options, method: 'DELETE' }),
  };
};

// 创建请求客户端
let clientInstance: null | RequestClient = null;

export const createRequestClient = async (): Promise<RequestClient> => {
  if (clientInstance) {
    return clientInstance;
  }

  if (moduleConfig.api.useMainAppClient) {
    try {
      // 尝试导入主应用的 requestClient
      const mainAppRequest = await importFromWebEle('api/request');
      if (mainAppRequest && mainAppRequest.requestClient) {
        console.warn('[Demo1] 成功导入主应用的 requestClient');
        clientInstance = mainAppRequest.requestClient;
        return clientInstance as RequestClient;
      }
    } catch (error) {
      console.warn(
        '[Demo1] 无法导入主应用的 requestClient，使用备用客户端:',
        error,
      );
    }
  }

  // 使用备用客户端
  console.warn('[Demo1] 使用备用 requestClient');
  clientInstance = createFallbackClient();
  return clientInstance;
};

// 获取请求客户端实例
export const getRequestClient = async (): Promise<RequestClient> => {
  return await createRequestClient();
};

// 重置客户端实例（用于测试或重新初始化）
export const resetRequestClient = () => {
  clientInstance = null;
};

// 导出默认客户端（延迟初始化）
let defaultClientPromise: null | Promise<RequestClient> = null;

export const requestClient = {
  async get(url: string, options?: any) {
    if (!defaultClientPromise) {
      defaultClientPromise = createRequestClient();
    }
    const client = await defaultClientPromise;
    return client.get(url, options);
  },

  async post(url: string, data?: any, options?: any) {
    if (!defaultClientPromise) {
      defaultClientPromise = createRequestClient();
    }
    const client = await defaultClientPromise;
    return client.post(url, data, options);
  },

  async put(url: string, data?: any, options?: any) {
    if (!defaultClientPromise) {
      defaultClientPromise = createRequestClient();
    }
    const client = await defaultClientPromise;
    return client.put(url, data, options);
  },

  async delete(url: string, options?: any) {
    if (!defaultClientPromise) {
      defaultClientPromise = createRequestClient();
    }
    const client = await defaultClientPromise;
    return client.delete(url, options);
  },
};
