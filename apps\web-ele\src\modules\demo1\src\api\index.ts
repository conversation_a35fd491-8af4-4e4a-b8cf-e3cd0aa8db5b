import type {
  ApiResponse,
  FormData,
  QueryParams,
  Statistics,
  UserInfo,
} from '../types';

// 创建请求客户端代理（在主应用中会被实际的 requestClient 替换）
const createRequestClient = () => {
  // 这是一个简化的实现，实际使用时会被主应用的 requestClient 替换
  const request = async (url: string, options: any = {}) => {
    const { method = 'GET', data, params } = options;

    // 构建查询字符串
    let queryString = '';
    if (params) {
      queryString = `?${new URLSearchParams(params).toString()}`;
    }

    const fetchOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    if (data && method !== 'GET') {
      fetchOptions.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(`${url}${queryString}`, fetchOptions);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  };

  return {
    get: (url: string, options?: any) =>
      request(url, { ...options, method: 'GET' }),
    post: (url: string, data?: any, options?: any) =>
      request(url, { ...options, method: 'POST', data }),
    put: (url: string, data?: any, options?: any) =>
      request(url, { ...options, method: 'PUT', data }),
    delete: (url: string, options?: any) =>
      request(url, { ...options, method: 'DELETE' }),
  };
};

// 导出请求客户端
export const requestClient = createRequestClient();

/**
 * Demo1 模块 API 服务
 */

// 获取用户信息
export const getUserInfo = async (
  userId: number,
): Promise<ApiResponse<UserInfo>> => {
  return requestClient.get(`/demo1/user/${userId}`);
};

// 更新用户信息
export const updateUserInfo = async (
  userId: number,
  data: Partial<UserInfo>,
): Promise<ApiResponse<UserInfo>> => {
  return requestClient.put(`/demo1/user/${userId}`, data);
};

// 获取统计数据
export const getStatistics = async (): Promise<ApiResponse<Statistics>> => {
  return requestClient.get('/demo1/statistics');
};

// 刷新统计数据
export const refreshStatistics = async (): Promise<ApiResponse<Statistics>> => {
  return requestClient.post('/demo1/statistics/refresh');
};

// 获取用户列表
export const getUserList = async (
  params: QueryParams,
): Promise<
  ApiResponse<{
    list: UserInfo[];
    total: number;
  }>
> => {
  const queryString = new URLSearchParams(params as any).toString();
  const url = queryString ? `/demo1/users?${queryString}` : '/demo1/users';
  return requestClient.get(url);
};

// 创建用户
export const createUser = async (
  data: Omit<UserInfo, 'id'>,
): Promise<ApiResponse<UserInfo>> => {
  return requestClient.post('/demo1/user', data);
};

// 删除用户
export const deleteUser = async (
  userId: number,
): Promise<ApiResponse<void>> => {
  return requestClient.delete(`/demo1/user/${userId}`);
};

// 提交表单数据
export const submitForm = async (
  data: FormData,
): Promise<ApiResponse<void>> => {
  return requestClient.post('/demo1/form/submit', data);
};

// 获取表单配置
export const getFormConfig = async (): Promise<
  ApiResponse<{
    fields: Array<{
      label: string;
      name: string;
      options?: Array<{ label: string; value: string }>;
      required: boolean;
      type: string;
    }>;
  }>
> => {
  return requestClient.get('/demo1/form/config');
};

// 上传文件
export const uploadFile = async (
  file: File,
): Promise<
  ApiResponse<{
    filename: string;
    size: number;
    url: string;
  }>
> => {
  const formData = new FormData();
  formData.append('file', file);

  return requestClient.post('/demo1/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// 导出数据
export const exportData = async (params: {
  filters?: Record<string, any>;
  type: 'csv' | 'excel' | 'pdf';
}): Promise<Blob> => {
  const queryString = new URLSearchParams(params as any).toString();
  const url = queryString ? `/demo1/export?${queryString}` : '/demo1/export';

  // 注意：这里返回的是 JSON，实际使用时需要处理 Blob
  const response = await requestClient.get(url);
  return new Blob([JSON.stringify(response)], { type: 'application/json' });
};

// 获取模块配置
export const getModuleConfig = async (): Promise<
  ApiResponse<{
    features: string[];
    settings: Record<string, any>;
    version: string;
  }>
> => {
  return requestClient.get('/demo1/config');
};

// 更新模块配置
export const updateModuleConfig = async (
  config: Record<string, any>,
): Promise<ApiResponse<void>> => {
  return requestClient.put('/demo1/config', config);
};
