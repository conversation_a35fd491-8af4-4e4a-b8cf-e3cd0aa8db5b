import { requestClient } from '#/api/request';
import type { 
  UserInfo, 
  Statistics, 
  ApiResponse, 
  QueryParams, 
  FormData 
} from '../types';

/**
 * Demo1 模块 API 服务
 */

// 获取用户信息
export const getUserInfo = async (userId: number): Promise<ApiResponse<UserInfo>> => {
  return requestClient.get(`/demo1/user/${userId}`);
};

// 更新用户信息
export const updateUserInfo = async (userId: number, data: Partial<UserInfo>): Promise<ApiResponse<UserInfo>> => {
  return requestClient.put(`/demo1/user/${userId}`, data);
};

// 获取统计数据
export const getStatistics = async (): Promise<ApiResponse<Statistics>> => {
  return requestClient.get('/demo1/statistics');
};

// 刷新统计数据
export const refreshStatistics = async (): Promise<ApiResponse<Statistics>> => {
  return requestClient.post('/demo1/statistics/refresh');
};

// 获取用户列表
export const getUserList = async (params: QueryParams): Promise<ApiResponse<{
  list: UserInfo[];
  total: number;
}>> => {
  return requestClient.get('/demo1/users', { params });
};

// 创建用户
export const createUser = async (data: Omit<UserInfo, 'id'>): Promise<ApiResponse<UserInfo>> => {
  return requestClient.post('/demo1/user', data);
};

// 删除用户
export const deleteUser = async (userId: number): Promise<ApiResponse<void>> => {
  return requestClient.delete(`/demo1/user/${userId}`);
};

// 提交表单数据
export const submitForm = async (data: FormData): Promise<ApiResponse<void>> => {
  return requestClient.post('/demo1/form/submit', data);
};

// 获取表单配置
export const getFormConfig = async (): Promise<ApiResponse<{
  fields: Array<{
    name: string;
    type: string;
    label: string;
    required: boolean;
    options?: Array<{ label: string; value: string }>;
  }>;
}>> => {
  return requestClient.get('/demo1/form/config');
};

// 上传文件
export const uploadFile = async (file: File): Promise<ApiResponse<{
  url: string;
  filename: string;
  size: number;
}>> => {
  const formData = new FormData();
  formData.append('file', file);
  
  return requestClient.post('/demo1/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// 导出数据
export const exportData = async (params: {
  type: 'excel' | 'csv' | 'pdf';
  filters?: Record<string, any>;
}): Promise<Blob> => {
  const response = await requestClient.get('/demo1/export', {
    params,
    responseType: 'blob',
  });
  return response.data;
};

// 获取模块配置
export const getModuleConfig = async (): Promise<ApiResponse<{
  version: string;
  features: string[];
  settings: Record<string, any>;
}>> => {
  return requestClient.get('/demo1/config');
};

// 更新模块配置
export const updateModuleConfig = async (config: Record<string, any>): Promise<ApiResponse<void>> => {
  return requestClient.put('/demo1/config', config);
};
