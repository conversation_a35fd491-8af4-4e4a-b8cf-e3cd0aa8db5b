import type {
  UserInfo,
  Statistics,
  ApiResponse,
  QueryParams,
  FormData
} from '../types';
import { moduleConfig } from '../config';

// 简单的请求客户端（在实际使用时会被主应用的 requestClient 替换）
const createRequestClient = () => {
  const baseURL = moduleConfig.api.baseURL;

  const request = async (url: string, options: RequestInit = {}) => {
    const response = await fetch(`${baseURL}${url}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  };

  return {
    get: (url: string, options?: RequestInit) => request(url, { ...options, method: 'GET' }),
    post: (url: string, data?: any, options?: RequestInit) =>
      request(url, { ...options, method: 'POST', body: JSON.stringify(data) }),
    put: (url: string, data?: any, options?: RequestInit) =>
      request(url, { ...options, method: 'PUT', body: JSON.stringify(data) }),
    delete: (url: string, options?: RequestInit) => request(url, { ...options, method: 'DELETE' }),
  };
};

// 导出请求客户端实例
export const requestClient = createRequestClient();

/**
 * Demo1 模块 API 服务
 */

// 获取用户信息
export const getUserInfo = async (userId: number): Promise<ApiResponse<UserInfo>> => {
  return requestClient.get(`/demo1/user/${userId}`);
};

// 更新用户信息
export const updateUserInfo = async (userId: number, data: Partial<UserInfo>): Promise<ApiResponse<UserInfo>> => {
  return requestClient.put(`/demo1/user/${userId}`, data);
};

// 获取统计数据
export const getStatistics = async (): Promise<ApiResponse<Statistics>> => {
  return requestClient.get('/demo1/statistics');
};

// 刷新统计数据
export const refreshStatistics = async (): Promise<ApiResponse<Statistics>> => {
  return requestClient.post('/demo1/statistics/refresh');
};

// 获取用户列表
export const getUserList = async (params: QueryParams): Promise<ApiResponse<{
  list: UserInfo[];
  total: number;
}>> => {
  const queryString = new URLSearchParams(params as any).toString();
  const url = queryString ? `/demo1/users?${queryString}` : '/demo1/users';
  return requestClient.get(url);
};

// 创建用户
export const createUser = async (data: Omit<UserInfo, 'id'>): Promise<ApiResponse<UserInfo>> => {
  return requestClient.post('/demo1/user', data);
};

// 删除用户
export const deleteUser = async (userId: number): Promise<ApiResponse<void>> => {
  return requestClient.delete(`/demo1/user/${userId}`);
};

// 提交表单数据
export const submitForm = async (data: FormData): Promise<ApiResponse<void>> => {
  return requestClient.post('/demo1/form/submit', data);
};

// 获取表单配置
export const getFormConfig = async (): Promise<ApiResponse<{
  fields: Array<{
    name: string;
    type: string;
    label: string;
    required: boolean;
    options?: Array<{ label: string; value: string }>;
  }>;
}>> => {
  return requestClient.get('/demo1/form/config');
};

// 上传文件
export const uploadFile = async (file: File): Promise<ApiResponse<{
  url: string;
  filename: string;
  size: number;
}>> => {
  const formData = new FormData();
  formData.append('file', file);

  return requestClient.post('/demo1/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// 导出数据
export const exportData = async (params: {
  type: 'excel' | 'csv' | 'pdf';
  filters?: Record<string, any>;
}): Promise<Blob> => {
  const queryString = new URLSearchParams(params as any).toString();
  const url = queryString ? `/demo1/export?${queryString}` : '/demo1/export';

  // 注意：这里返回的是 JSON，实际使用时需要处理 Blob
  const response = await requestClient.get(url);
  return new Blob([JSON.stringify(response)], { type: 'application/json' });
};

// 获取模块配置
export const getModuleConfig = async (): Promise<ApiResponse<{
  version: string;
  features: string[];
  settings: Record<string, any>;
}>> => {
  return requestClient.get('/demo1/config');
};

// 更新模块配置
export const updateModuleConfig = async (config: Record<string, any>): Promise<ApiResponse<void>> => {
  return requestClient.put('/demo1/config', config);
};
