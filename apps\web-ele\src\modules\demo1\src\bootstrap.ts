/**
 * Demo1 模块启动文件
 * 用于独立运行时的初始化
 */

import { createApp } from 'vue';
import { createRouter, createWebHistory } from 'vue-router';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';

import { getModuleConfig, validateConfig } from './config';
import { moduleRoutes } from './index';

// 导入样式
import 'element-plus/dist/index.css';

/**
 * 创建 Demo1 模块应用实例
 */
export async function createDemo1App() {
  // 验证配置
  validateConfig();
  const config = getModuleConfig();
  
  console.log(`[Demo1] 模块启动中... 版本: ${config.version}`);
  
  // 创建应用实例
  const app = createApp({
    template: `
      <div id="demo1-app">
        <h1>{{ title }}</h1>
        <router-view />
      </div>
    `,
    data() {
      return {
        title: config.title,
      };
    },
  });
  
  // 创建路由
  const router = createRouter({
    history: createWebHistory('/demo1/'),
    routes: moduleRoutes.map(route => ({
      ...route,
      component: () => import(route.component),
    })),
  });
  
  // 创建状态管理
  const pinia = createPinia();
  
  // 注册插件
  app.use(router);
  app.use(pinia);
  app.use(ElementPlus);
  
  // 全局配置
  app.config.globalProperties.$config = config;
  
  // 错误处理
  app.config.errorHandler = (err, vm, info) => {
    console.error('[Demo1] 应用错误:', err, info);
  };
  
  console.log(`[Demo1] 模块启动完成`);
  
  return { app, router, pinia, config };
}

/**
 * 挂载 Demo1 模块应用
 */
export async function mountDemo1App(selector = '#app') {
  const { app } = await createDemo1App();
  
  const container = document.querySelector(selector);
  if (!container) {
    throw new Error(`[Demo1] 找不到挂载容器: ${selector}`);
  }
  
  app.mount(container);
  
  return app;
}

/**
 * 模块热重载支持
 */
if (import.meta.hot) {
  import.meta.hot.accept(() => {
    console.log('[Demo1] 模块热重载');
  });
}
