/**
 * Demo1 模块配置文件
 */

// 模块基础配置
export const moduleConfig = {
  // 基础信息
  name: 'demo1',
  title: 'Demo1 模块',
  version: '0.0.1',
  description: 'Demo1 功能模块',

  // 路径配置
  paths: {
    // 指向 web-ele 根目录
    webEleRoot: '../../../',
    // 指向 web-ele 的 API 目录
    webEleApi: '../../../api',
    // 指向 web-ele 的 utils 目录
    webEleUtils: '../../../utils',
    // 指向 web-ele 的 store 目录
    webEleStore: '../../../store',
  },

  // API 配置
  api: {
    baseURL: '/api/demo1',
    timeout: 10000,
    // 是否使用主应用的 requestClient
    useMainAppClient: true,
  },

  // 功能开关
  features: {
    charts: true,
    export: true,
    import: true,
    realtime: false,
  },

  // 主题配置
  theme: {
    primaryColor: '#409EFF',
    borderRadius: '6px',
  },

  // 业务配置
  business: {
    maxUploadSize: 10 * 1024 * 1024, // 10MB
    cacheDuration: 60 * 60 * 1000, // 1小时
    pageSize: 20,
    maxRetries: 3,
  },
} as const;

// 运行时配置（可动态修改）
export const runtimeConfig = {
  // 动态配置
  settings: {
    autoSave: true,
    pageSize: 20,
    refreshInterval: 30000,
    theme: 'light',
  },

  // 缓存配置
  cache: new Map<string, any>(),
};

// 获取完整配置
export function getModuleConfig() {
  return {
    ...moduleConfig,
    runtime: runtimeConfig,
  };
}

// 更新运行时配置
export function updateRuntimeConfig(updates: Partial<typeof runtimeConfig.settings>) {
  Object.assign(runtimeConfig.settings, updates);
}

// 缓存操作
export function setCache(key: string, value: any, ttl?: number) {
  runtimeConfig.cache.set(key, {
    value,
    timestamp: Date.now(),
    ttl: ttl || moduleConfig.business.cacheDuration,
  });
}

export function getCache(key: string) {
  const item = runtimeConfig.cache.get(key);
  if (!item) return null;

  if (Date.now() - item.timestamp > item.ttl) {
    runtimeConfig.cache.delete(key);
    return null;
  }

  return item.value;
}

export function clearCache() {
  runtimeConfig.cache.clear();
}

// 路径解析工具函数
export function resolveWebElePath(relativePath: string): string {
  return `${moduleConfig.paths.webEleRoot}${relativePath}`;
}

export function resolveWebEleApiPath(apiPath: string): string {
  return `${moduleConfig.paths.webEleApi}/${apiPath}`;
}

export function resolveWebEleUtilsPath(utilPath: string): string {
  return `${moduleConfig.paths.webEleUtils}/${utilPath}`;
}

export function resolveWebEleStorePath(storePath: string): string {
  return `${moduleConfig.paths.webEleStore}/${storePath}`;
}

// 动态导入主应用模块的工具函数
export async function importFromWebEle(modulePath: string) {
  try {
    const fullPath = resolveWebElePath(modulePath);
    return await import(/* @vite-ignore */ fullPath);
  } catch (error) {
    console.error(`Failed to import from web-ele: ${modulePath}`, error);
    throw error;
  }
}

// 导出类型
export type ModuleConfig = typeof moduleConfig;
export type RuntimeConfig = typeof runtimeConfig;
