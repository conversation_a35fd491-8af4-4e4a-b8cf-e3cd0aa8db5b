/**
 * Demo1 模块配置文件
 * 支持环境变量和运行时配置
 */

// 环境变量类型定义
interface Demo1Env {
  VITE_APP_TITLE: string;
  VITE_APP_VERSION: string;
  VITE_APP_MODULE_NAME: string;
  VITE_API_BASE_URL: string;
  VITE_API_TIMEOUT: string;
  VITE_ENABLE_MOCK: string;
  VITE_ENABLE_DEBUG: string;
  VITE_ENABLE_ANALYTICS: string;
  VITE_DEMO1_THEME_COLOR: string;
  VITE_DEMO1_MAX_UPLOAD_SIZE: string;
  VITE_DEMO1_CACHE_DURATION: string;
}

// 获取环境变量
const env = import.meta.env as unknown as Demo1Env;

// 模块基础配置
export const moduleConfig = {
  // 基础信息
  name: env.VITE_APP_MODULE_NAME || 'demo1',
  title: env.VITE_APP_TITLE || 'Demo1 模块',
  version: env.VITE_APP_VERSION || '0.0.1',
  
  // API 配置
  api: {
    baseURL: env.VITE_API_BASE_URL || '/api/demo1',
    timeout: Number(env.VITE_API_TIMEOUT) || 10000,
    mockEnabled: env.VITE_ENABLE_MOCK === 'true',
  },
  
  // 功能开关
  features: {
    debug: env.VITE_ENABLE_DEBUG === 'true',
    analytics: env.VITE_ENABLE_ANALYTICS === 'true',
    mock: env.VITE_ENABLE_MOCK === 'true',
  },
  
  // 主题配置
  theme: {
    primaryColor: env.VITE_DEMO1_THEME_COLOR || '#409EFF',
  },
  
  // 业务配置
  business: {
    maxUploadSize: Number(env.VITE_DEMO1_MAX_UPLOAD_SIZE) || 10 * 1024 * 1024, // 10MB
    cacheDuration: Number(env.VITE_DEMO1_CACHE_DURATION) || 60 * 60 * 1000, // 1小时
  },
} as const;

// 运行时配置（可动态修改）
export const runtimeConfig = {
  // 当前环境
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
  
  // 构建信息
  buildTime: __DEMO1_BUILD_TIME__,
  version: __DEMO1_VERSION__,
  
  // 动态配置
  settings: {
    autoSave: true,
    pageSize: 20,
    refreshInterval: 30000,
  },
};

// 配置验证函数
export function validateConfig() {
  const errors: string[] = [];
  
  if (!moduleConfig.name) {
    errors.push('模块名称不能为空');
  }
  
  if (!moduleConfig.api.baseURL) {
    errors.push('API 基础地址不能为空');
  }
  
  if (moduleConfig.api.timeout <= 0) {
    errors.push('API 超时时间必须大于 0');
  }
  
  if (errors.length > 0) {
    throw new Error(`Demo1 模块配置验证失败: ${errors.join(', ')}`);
  }
  
  return true;
}

// 获取完整配置
export function getModuleConfig() {
  validateConfig();
  return {
    ...moduleConfig,
    runtime: runtimeConfig,
  };
}

// 更新运行时配置
export function updateRuntimeConfig(updates: Partial<typeof runtimeConfig.settings>) {
  Object.assign(runtimeConfig.settings, updates);
}

// 导出类型
export type ModuleConfig = typeof moduleConfig;
export type RuntimeConfig = typeof runtimeConfig;
