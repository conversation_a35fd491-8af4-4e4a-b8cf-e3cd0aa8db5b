// Demo1 模块导出文件
export { default as Demo1Overview } from './views/index.vue';
export { default as Demo1Element } from './views/element/index.vue';
export { default as Demo1Form } from './views/form/basic.vue';

// 模块信息
export const moduleInfo = {
  name: 'demo1',
  version: '1.0.0',
  description: 'Demo1 分包模块',
  team: 'B小组',
};

// 模块路由配置
export const moduleRoutes = [
  {
    name: 'Demo1Overview',
    path: '/demo1/overview',
    component: '/modules/demo1/views/index',
    meta: {
      title: '模块概览',
    },
  },
  {
    name: 'Demo1Element',
    path: '/demo1/element',
    component: '/modules/demo1/views/element/index',
    meta: {
      title: 'Element Plus演示',
    },
  },
  {
    name: 'Demo1Form',
    path: '/demo1/form',
    component: '/modules/demo1/views/form/basic',
    meta: {
      title: '表单演示',
    },
  },
];
