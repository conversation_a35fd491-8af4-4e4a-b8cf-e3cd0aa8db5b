/**
 * Demo1 模块主导出文件
 * 提供模块的所有公共接口
 */

// 组件导出
export { default as Demo1Overview } from './views/index.vue';
export { default as Demo1Element } from './views/element/index.vue';
export { default as Demo1Form } from './views/form/basic.vue';

// 配置导出
export {
  moduleConfig,
  runtimeConfig,
  getModuleConfig,
  updateRuntimeConfig,
  setCache,
  getCache,
  clearCache,
  resolveWebElePath,
  resolveWebEleApiPath,
  resolveWebEleUtilsPath,
  resolveWebEleStorePath,
  importFromWebEle
} from './config';
export type { ModuleConfig, RuntimeConfig } from './config';

// API 客户端导出
export { createRequestClient, getRequestClient, resetRequestClient } from './api/client';

// 内部导入（用于模块初始化）
import { getModuleConfig } from './config';

// Store 导出
export { useDemo1Store } from './store';
export type { Demo1State } from './store';

// API 导出
export * from './api';

// 工具函数导出
export * from './utils';

// 类型导出
export * from './types';

// 模块信息
export const moduleInfo = {
  name: 'demo1',
  version: '1.0.0',
  description: 'Demo1 子模块 - 支持独立开发和配置',
  team: 'B小组',
  features: [
    '独立开发环境',
    '自定义配置',
    '热重载支持',
    '类型安全',
    '环境变量管理',
  ],
  dependencies: {
    vue: '^3.0.0',
    'vue-router': '^4.0.0',
    pinia: '^2.0.0',
    'element-plus': '^2.0.0',
  },
} as const;

// 模块路由配置
export const moduleRoutes = [
  {
    name: 'Demo1Overview',
    path: '/demo1/overview',
    component: () => import('./views/index.vue'),
    meta: {
      title: '模块概览',
      icon: 'ep:home-filled',
      order: 1,
    },
  },
  {
    name: 'Demo1Element',
    path: '/demo1/element',
    component: () => import('./views/element/index.vue'),
    meta: {
      title: 'Element Plus演示',
      icon: 'ep:element-plus',
      order: 2,
    },
  },
  {
    name: 'Demo1Form',
    path: '/demo1/form',
    component: () => import('./views/form/basic.vue'),
    meta: {
      title: '表单演示',
      icon: 'ep:document',
      order: 3,
    },
  },
] as const;

// 模块生命周期钩子
export const moduleHooks = {
  // 模块安装时调用
  onInstall: () => {
    console.log('[Demo1] 模块已安装');
  },

  // 模块卸载时调用
  onUninstall: () => {
    console.log('[Demo1] 模块已卸载');
  },

  // 模块激活时调用
  onActivate: () => {
    console.log('[Demo1] 模块已激活');
  },

  // 模块停用时调用
  onDeactivate: () => {
    console.log('[Demo1] 模块已停用');
  },
} as const;

// 模块初始化函数
export const initDemo1Module = () => {
  moduleHooks.onInstall();
  const config = getModuleConfig();
  return {
    info: moduleInfo,
    routes: moduleRoutes,
    store: 'useDemo1Store', // 返回 store 名称而不是实例
    config,
  };
};

// 默认导出
export default {
  moduleInfo,
  moduleRoutes,
  moduleHooks,
  initDemo1Module,
};
