/**
 * Demo1 模块环境变量类型定义
 */

/// <reference types="vite/client" />

declare global {
  const __DEMO1_VERSION__: string;
  const __DEMO1_BUILD_TIME__: string;
}

interface ImportMetaEnv {
  // 基础配置
  readonly VITE_APP_TITLE: string;
  readonly VITE_APP_VERSION: string;
  readonly VITE_APP_MODULE_NAME: string;
  readonly VITE_APP_ENV: string;
  
  // API 配置
  readonly VITE_API_BASE_URL: string;
  readonly VITE_API_TIMEOUT: string;
  readonly VITE_API_MOCK_ENABLED: string;
  
  // 功能开关
  readonly VITE_ENABLE_MOCK: string;
  readonly VITE_ENABLE_DEBUG: string;
  readonly VITE_ENABLE_ANALYTICS: string;
  readonly VITE_ENABLE_DEVTOOLS: string;
  readonly VITE_ENABLE_HOT_RELOAD: string;
  
  // 开发服务器配置
  readonly VITE_DEV_PORT: string;
  readonly VITE_DEV_HOST: string;
  
  // Demo1 特定配置
  readonly VITE_DEMO1_THEME_COLOR: string;
  readonly VITE_DEMO1_MAX_UPLOAD_SIZE: string;
  readonly VITE_DEMO1_CACHE_DURATION: string;
  
  // 构建配置
  readonly VITE_BUILD_SOURCEMAP: string;
  readonly VITE_BUILD_MINIFY: string;
  
  // 日志配置
  readonly VITE_LOG_LEVEL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

export {};
