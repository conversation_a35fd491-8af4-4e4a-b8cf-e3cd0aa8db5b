/**
 * Demo1 模块类型定义
 */

// 用户信息类型
export interface UserInfo {
  id: number;
  name: string;
  email: string;
  avatar: string;
  createdAt?: string;
  updatedAt?: string;
}

// 统计数据类型
export interface Statistics {
  totalUsers: number;
  totalOrders: number;
  totalRevenue: number;
  lastUpdated: string;
}

// 设置类型
export interface Settings {
  theme: 'dark' | 'light';
  language: 'en-US' | 'zh-CN';
  autoRefresh: boolean;
  refreshInterval: number;
}

// 功能项类型
export interface Feature {
  id: number;
  name: string;
  description?: string;
  enabled?: boolean;
}

// 表格数据类型
export interface TableData {
  date: string;
  name: string;
  address: string;
}

// 表单数据类型
export interface FormData {
  username: string;
  email: string;
  age: number;
  gender: 'female' | 'male';
  hobbies: string[];
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

// 分页参数类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

// 查询参数类型
export interface QueryParams extends PaginationParams {
  keyword?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
}

// 模块配置类型
export interface ModuleConfig {
  name: string;
  version: string;
  description: string;
  team: string;
  dependencies: string[];
  routes: RouteConfig[];
}

// 路由配置类型
export interface RouteConfig {
  name: string;
  path: string;
  component: string;
  meta: {
    icon?: string;
    keepAlive?: boolean;
    requireAuth?: boolean;
    title: string;
  };
}

// 事件类型
export type EventType =
  | 'change'
  | 'click'
  | 'error'
  | 'load'
  | 'reset'
  | 'submit';

// 回调函数类型
export type CallbackFunction<T = any> = (data: T) => void;

// 异步回调函数类型
export type AsyncCallbackFunction<T = any> = (data: T) => Promise<void>;
