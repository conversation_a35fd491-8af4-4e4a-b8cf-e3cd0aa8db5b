import dayjs from 'dayjs';
import { cloneDeep, debounce, throttle } from 'lodash-es';

/**
 * 格式化时间
 */
export const formatTime = (date: Date | string | number, format = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(date).format(format);
};

/**
 * 获取当前时间戳
 */
export const getCurrentTimestamp = () => {
  return dayjs().valueOf();
};

/**
 * 深拷贝对象
 */
export const deepClone = <T>(obj: T): T => {
  return cloneDeep(obj);
};

/**
 * 防抖函数
 */
export const createDebounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
) => {
  return debounce(func, wait);
};

/**
 * 节流函数
 */
export const createThrottle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
) => {
  return throttle(func, wait);
};

/**
 * 格式化货币
 */
export const formatCurrency = (amount: number, currency = 'CNY') => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency,
  }).format(amount);
};

/**
 * 生成随机ID
 */
export const generateId = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

/**
 * 延迟执行
 */
export const delay = (ms: number) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};
