<script setup lang="ts">
import { reactive } from 'vue';

import { ElMessage } from 'element-plus';

const form = reactive({
  username: '',
  email: '',
  age: 18,
  gender: 'male',
  hobbies: [],
});

const onSubmit = () => {
  ElMessage.success('表单提交成功！');
};

const onReset = () => {
  form.username = '';
  form.email = '';
  form.age = 18;
  form.gender = 'male';
  form.hobbies = [];
  ElMessage.info('表单已重置');
};
</script>

<template>
  <div class="p-4">
    <h1 class="mb-4 text-2xl font-bold">表单演示</h1>

    <el-form :model="form" label-width="120px">
      <el-form-item label="用户名">
        <el-input v-model="form.username" />
      </el-form-item>

      <el-form-item label="邮箱">
        <el-input v-model="form.email" type="email" />
      </el-form-item>

      <el-form-item label="年龄">
        <el-input-number v-model="form.age" :min="1" :max="100" />
      </el-form-item>

      <el-form-item label="性别">
        <el-radio-group v-model="form.gender">
          <el-radio value="male">男</el-radio>
          <el-radio value="female">女</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="爱好">
        <el-checkbox-group v-model="form.hobbies">
          <el-checkbox value="reading">阅读</el-checkbox>
          <el-checkbox value="music">音乐</el-checkbox>
          <el-checkbox value="sports">运动</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSubmit">提交</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
