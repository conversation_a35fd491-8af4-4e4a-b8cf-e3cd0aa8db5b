{"$schema": "https://json.schemastore.org/tsconfig", "extends": "../../../tsconfig.json", "compilerOptions": {"baseUrl": "../../../", "paths": {"#/*": ["./src/*"], "@/*": ["./src/modules/demo1/src/*"], "@modules/*": ["./src/modules/*"], "@demo1/*": ["./src/modules/demo1/src/*"], "@vben/*": ["../../../packages/*"]}, "types": ["vite/client", "element-plus/global"], "noEmit": true, "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.d.ts"], "exclude": ["node_modules", "dist"]}