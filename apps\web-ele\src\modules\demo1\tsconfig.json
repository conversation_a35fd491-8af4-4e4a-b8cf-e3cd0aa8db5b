{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@vben/tsconfig/web-app.json", "compilerOptions": {"jsx": "preserve", "lib": ["ES2020", "DOM", "DOM.Iterable"], "baseUrl": ".", "moduleResolution": "bundler", "paths": {"#/*": ["./src/*"], "@/*": ["./src/*"], "@modules/*": ["../*"], "@app/*": ["../../../*"]}, "resolveJsonModule": true, "types": ["vite/client", "element-plus/global"], "allowJs": true, "strict": true, "noEmit": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.d.ts", "vite.config.mts", ".env*"], "exclude": ["node_modules", "dist"]}