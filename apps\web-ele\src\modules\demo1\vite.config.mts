import { fileURLToPath } from 'node:url';

import vue from '@vitejs/plugin-vue';
import ElementPlus from 'unplugin-element-plus/vite';
import { defineConfig } from 'vite';

export default defineConfig({
  // 构建配置
  build: {
    lib: {
      entry: 'src/index.ts',
      fileName: (format) => `demo1.${format}.js`,
      formats: ['es', 'umd'],
      name: 'Demo1Module',
    },
    outDir: 'dist',
    rollupOptions: {
      // 外部化依赖，避免打包到模块中
      external: ['vue', 'vue-router', 'pinia', 'element-plus'],
      output: {
        globals: {
          'element-plus': 'ElementPlus',
          pinia: 'Pinia',
          vue: 'Vue',
          'vue-router': 'VueRouter',
        },
      },
    },
    sourcemap: true,
  },

  // 环境变量配置
  define: {
    __DEMO1_BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    __DEMO1_VERSION__: JSON.stringify('0.0.1'),
  },

  plugins: [
    vue(),
    ElementPlus({
      format: 'esm',
    }),
  ],

  resolve: {
    alias: {
      // 模块内部别名
      '#': fileURLToPath(new URL('src', import.meta.url)),
      '@': fileURLToPath(new URL('src', import.meta.url)),
      // 访问主应用资源
      '@app': fileURLToPath(new URL('../../../', import.meta.url)),
      // 继承主应用的模块别名
      '@modules': fileURLToPath(new URL('../', import.meta.url)),
    },
  },

  // 根目录和入口文件
  root: '.',

  // 开发服务器配置
  server: {
    host: true,
    port: 5174,
    proxy: {
      // 继承主应用的通用 API 代理
      '/api': {
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        target: 'http://localhost:8081',
        ws: true,
      },
      // Demo1 模块专用 API 代理
      '/api/demo1': {
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/demo1/, '/demo1'),
        target: 'http://localhost:8081',
        ws: true,
      },
    },
  },
});
