import type { RouteRecordStringComponent } from '@vben/types';

/**
 * 该文件放非后台返回的路由 比如个人中心 等需要跳转显示的页面
 */
const localRoutes: RouteRecordStringComponent[] = [
  {
    component: '/tool/gen/edit-gen/index',
    meta: {
      activePath: '/tool/gen',
      icon: 'tabler:code',
      title: '生成配置',
      hideInMenu: true,
    },
    name: 'GenConfig',
    path: '/tool/gen/edit-gen/:tableId',
  },
  {
    component: '/system/role-assign/index',
    meta: {
      activePath: '/system/role',
      icon: 'eos-icons:role-binding-outlined',
      title: '分配角色',
      hideInMenu: true,
    },
    name: 'RoleAssign',
    path: '/system/role-assign/:roleId',
  },
  {
    component: '/system/dict/dict-data/index',
    meta: {
      activePath: '/system/dict',
      icon: 'carbon:data-base',
      title: '字典数据',
      hideInMenu: true,
    },
    name: 'DictData',
    path: '/system/dict-data/:dictId',
  },
  {
    component: '/monitor/job/job-log/index',
    meta: {
      activePath: '/monitor/job',
      icon: 'carbon:data-base',
      title: '调度日志',
      hideInMenu: true,
    },
    name: 'JobLog',
    path: '/monitor/job-log',
  },
  {
    component: '/system/user/user-center/index',
    meta: {
      icon: 'la:user-cog',
      title: '个人中心',
      hideInMenu: true,
    },
    name: 'UserCenter',
    path: '/user/center',
  },
];

/**
 * 这里放本地路由
 */
export const localMenuList: RouteRecordStringComponent[] = [
  {
    component: 'BasicLayout',
    meta: {
      order: -1,
      title: 'page.dashboard.title',
      // 不使用基础布局（仅在顶级生效）
      noBasicLayout: true,
    },
    name: 'Dashboard',
    path: '/',
    redirect: '/analytics',
    children: [
      {
        name: 'Analytics',
        path: '/analytics',
        component: '/dashboard/analytics/index',
        meta: {
          affixTab: true,
          title: 'page.dashboard.analytics',
        },
      },
      {
        name: 'Workspace',
        path: '/workspace',
        component: '/dashboard/workspace/index',
        meta: {
          title: 'page.dashboard.workspace',
        },
      },
      // {
      //   name: 'VbenDocument',
      //   path: '/vben-admin/document',
      //   component: 'IFrameView',
      //   meta: {
      //     icon: 'lucide:book-open-text',
      //     iframeSrc: 'https://dapdap.top',
      //     keepAlive: true,
      //     title: $t('demos.vben.document'),
      //   },
      // },
    ],
  },
  // {
  //   component: '/_core/about/index',
  //   meta: {
  //     icon: 'lucide:copyright',
  //     order: 9999,
  //     title: $t('demos.vben.about'),
  //   },
  //   name: 'About',
  //   path: '/vben-admin/about',
  // },
  // 最简单的 demo1 路由测试
  {
    component: 'BasicLayout',
    meta: {
      icon: 'ic:baseline-science',
      order: 2000,
      title: 'Demo111模块',
    },
    name: 'Demo1',
    path: '/demo1',
    children: [
      {
        name: 'Demo1Overview',
        path: '/demo1/overview',
        component: '/modules/demo1/src/views/index',
        meta: {
          title: '模块概览',
        },
      },
      {
        name: 'Demo1Element',
        path: '/demo1/element',
        component: '/modules/demo1/src/views/element/index',
        meta: {
          title: 'Element Plus演示',
        },
      },
      {
        name: 'Demo1Form',
        path: '/demo1/form',
        component: '/modules/demo1/src/views/form/basic',
        meta: {
          title: '表单演示',
        },
      },
    ],
  },
  ...localRoutes,
];
